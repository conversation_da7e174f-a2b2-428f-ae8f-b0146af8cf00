/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
}

.nav-links {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.nav-link {
  display: inline-block;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-link:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
}

/* Controls section */
.controls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 15px;
}

.control-row:last-child {
  margin-bottom: 0;
}

/* Search input */
.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 45px;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #8899a6;
  font-size: 16px;
}

/* Template select */
.template-select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  min-width: 200px;
  transition: all 0.3s ease;
}

.template-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Buttons */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

/* Status messages */
.status-message {
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-weight: 500;
  display: none;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-message.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left: 4px solid #28a745;
}

.status-message.error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.status-message.info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

/* Chat list */
.chat-list-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;
}

.entity-list {
  display: grid;
  gap: 12px;
}

.entity-item {
  background: white;
  border: 2px solid #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.entity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.entity-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.entity-item:hover::before {
  opacity: 1;
}

.chat-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 15px;
  cursor: pointer;
  accent-color: #667eea;
  z-index: 1;
  position: relative;
}

.entity-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
  border: 3px solid #e9ecef;
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;
}

.entity-item:hover .entity-avatar {
  border-color: #667eea;
  transform: scale(1.05);
}

.entity-info {
  flex: 1;
  z-index: 1;
  position: relative;
}

.entity-name {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 4px;
}

.entity-type {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.entity-id {
  font-size: 11px;
  color: #adb5bd;
  margin-top: 2px;
}

/* Type badges */
.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge.group {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #8e2de2;
}

.type-badge.friend {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #4facfe;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #495057;
}

.empty-state p {
  font-size: 1rem;
  line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .control-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    min-width: auto;
  }
  
  .btn {
    min-width: auto;
    width: 100%;
  }
  
  .entity-item {
    padding: 12px;
  }
  
  .entity-avatar {
    width: 40px;
    height: 40px;
  }
  
  .entity-name {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
  
  .header,
  .controls,
  .chat-list-container {
    padding: 15px;
  }
  
  .entity-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .chat-checkbox {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .entity-avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Scrollbar styling */
.entity-list::-webkit-scrollbar {
  width: 8px;
}

.entity-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.entity-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.entity-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.entity-item {
  animation: fadeInUp 0.3s ease forwards;
}

.entity-item:nth-child(1) { animation-delay: 0.1s; }
.entity-item:nth-child(2) { animation-delay: 0.2s; }
.entity-item:nth-child(3) { animation-delay: 0.3s; }
.entity-item:nth-child(4) { animation-delay: 0.4s; }
.entity-item:nth-child(5) { animation-delay: 0.5s; }

/* Pulse animation for loading */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.btn:disabled .loading {
  animation: pulse 2s infinite;
}

/* Improved focus states */
.search-input:focus,
.template-select:focus {
  transform: translateY(-1px);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Hover effects for better UX */
.entity-item:hover .entity-name {
  color: #667eea;
}

.entity-item:hover .type-badge {
  transform: scale(1.05);
}

/* Selection state */
.entity-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.entity-item.selected::before {
  opacity: 1;
}

/* Improved status messages */
.status-message {
  position: relative;
  overflow: hidden;
}

.status-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.status-message.success::before {
  left: 100%;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .header,
  .controls,
  .chat-list-container {
    background: rgba(52, 73, 94, 0.95);
    color: #ecf0f1;
  }

  .entity-item {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .entity-name {
    color: #ecf0f1;
  }

  .search-input,
  .template-select {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .search-input::placeholder {
    color: #95a5a6;
  }
}
